import { useState } from 'react';
import { 
  Settings as SettingsIcon, Save, Upload, Download, 
  Bell, Shield, Globe, Database, Mail, Key,
  Users, Palette, Monitor, Smartphone, Tablet
} from 'lucide-react';

const Settings = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    general: {
      siteName: 'HustleGPT',
      siteDescription: 'AI-powered platform for entrepreneurs and side hustlers',
      siteUrl: 'https://hustlegpt.com',
      adminEmail: '<EMAIL>',
      timezone: 'UTC-5',
      language: 'en',
      maintenanceMode: false
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      weeklyReports: true,
      securityAlerts: true,
      userRegistrations: true,
      contentModeration: true
    },
    security: {
      twoFactorAuth: true,
      sessionTimeout: 30,
      passwordExpiry: 90,
      loginAttempts: 5,
      ipWhitelist: '',
      sslEnabled: true,
      encryptionLevel: 'AES-256'
    },
    api: {
      rateLimit: 1000,
      apiVersion: 'v1',
      webhookUrl: '',
      apiKey: 'sk-1234567890abcdef',
      allowCors: true,
      debugMode: false
    }
  });

  const tabs = [
    { id: 'general', name: 'General', icon: SettingsIcon },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'api', name: 'API Settings', icon: Database }
  ];

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const handleSave = () => {
    // Save settings logic here
    console.log('Saving settings:', settings);
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-admin-700 mb-2">
            Site Name
          </label>
          <input
            type="text"
            value={settings.general.siteName}
            onChange={(e) => handleSettingChange('general', 'siteName', e.target.value)}
            className="admin-input"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-admin-700 mb-2">
            Admin Email
          </label>
          <input
            type="email"
            value={settings.general.adminEmail}
            onChange={(e) => handleSettingChange('general', 'adminEmail', e.target.value)}
            className="admin-input"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-admin-700 mb-2">
          Site Description
        </label>
        <textarea
          value={settings.general.siteDescription}
          onChange={(e) => handleSettingChange('general', 'siteDescription', e.target.value)}
          rows={3}
          className="admin-textarea"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-admin-700 mb-2">
            Site URL
          </label>
          <input
            type="url"
            value={settings.general.siteUrl}
            onChange={(e) => handleSettingChange('general', 'siteUrl', e.target.value)}
            className="admin-input"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-admin-700 mb-2">
            Timezone
          </label>
          <select
            value={settings.general.timezone}
            onChange={(e) => handleSettingChange('general', 'timezone', e.target.value)}
            className="admin-select"
          >
            <option value="UTC-12">UTC-12</option>
            <option value="UTC-8">UTC-8 (PST)</option>
            <option value="UTC-5">UTC-5 (EST)</option>
            <option value="UTC+0">UTC+0 (GMT)</option>
            <option value="UTC+1">UTC+1 (CET)</option>
            <option value="UTC+8">UTC+8 (CST)</option>
          </select>
        </div>
      </div>

      <div className="flex items-center justify-between p-4 bg-admin-50 rounded-lg">
        <div>
          <h4 className="font-medium text-admin-900">Maintenance Mode</h4>
          <p className="text-sm text-admin-600 mt-1">Enable to temporarily disable public access</p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.general.maintenanceMode}
            onChange={(e) => handleSettingChange('general', 'maintenanceMode', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-admin-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-admin-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
        </label>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      {Object.entries(settings.notifications).map(([key, value]) => (
        <div key={key} className="flex items-center justify-between p-4 bg-admin-50 rounded-lg">
          <div>
            <h4 className="font-medium text-admin-900 capitalize">
              {key.replace(/([A-Z])/g, ' $1').trim()}
            </h4>
            <p className="text-sm text-admin-600 mt-1">
              {key === 'emailNotifications' && 'Receive email notifications for important events'}
              {key === 'pushNotifications' && 'Browser push notifications for real-time updates'}
              {key === 'smsNotifications' && 'SMS alerts for critical system events'}
              {key === 'weeklyReports' && 'Weekly summary reports via email'}
              {key === 'securityAlerts' && 'Immediate alerts for security events'}
              {key === 'userRegistrations' && 'Notifications when new users register'}
              {key === 'contentModeration' && 'Alerts for content requiring moderation'}
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleSettingChange('notifications', key, e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-admin-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-admin-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
          </label>
        </div>
      ))}
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-admin-700 mb-2">
            Session Timeout (minutes)
          </label>
          <input
            type="number"
            value={settings.security.sessionTimeout}
            onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
            className="admin-input"
            min="5"
            max="480"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-admin-700 mb-2">
            Password Expiry (days)
          </label>
          <input
            type="number"
            value={settings.security.passwordExpiry}
            onChange={(e) => handleSettingChange('security', 'passwordExpiry', parseInt(e.target.value))}
            className="admin-input"
            min="30"
            max="365"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-admin-700 mb-2">
            Max Login Attempts
          </label>
          <input
            type="number"
            value={settings.security.loginAttempts}
            onChange={(e) => handleSettingChange('security', 'loginAttempts', parseInt(e.target.value))}
            className="admin-input"
            min="3"
            max="10"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-admin-700 mb-2">
            Encryption Level
          </label>
          <select
            value={settings.security.encryptionLevel}
            onChange={(e) => handleSettingChange('security', 'encryptionLevel', e.target.value)}
            className="admin-select"
          >
            <option value="AES-128">AES-128</option>
            <option value="AES-256">AES-256</option>
            <option value="RSA-2048">RSA-2048</option>
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-admin-700 mb-2">
          IP Whitelist (comma-separated)
        </label>
        <textarea
          value={settings.security.ipWhitelist}
          onChange={(e) => handleSettingChange('security', 'ipWhitelist', e.target.value)}
          rows={3}
          className="admin-textarea"
          placeholder="***********, ********, ..."
        />
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 bg-admin-50 rounded-lg">
          <div>
            <h4 className="font-medium text-admin-900">Two-Factor Authentication</h4>
            <p className="text-sm text-admin-600 mt-1">Require 2FA for all admin accounts</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.security.twoFactorAuth}
              onChange={(e) => handleSettingChange('security', 'twoFactorAuth', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-admin-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-admin-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between p-4 bg-admin-50 rounded-lg">
          <div>
            <h4 className="font-medium text-admin-900">SSL Encryption</h4>
            <p className="text-sm text-admin-600 mt-1">Force HTTPS for all connections</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.security.sslEnabled}
              onChange={(e) => handleSettingChange('security', 'sslEnabled', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-admin-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-admin-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
          </label>
        </div>
      </div>
    </div>
  );

  const renderApiSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-admin-700 mb-2">
            Rate Limit (requests/hour)
          </label>
          <input
            type="number"
            value={settings.api.rateLimit}
            onChange={(e) => handleSettingChange('api', 'rateLimit', parseInt(e.target.value))}
            className="admin-input"
            min="100"
            max="10000"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-admin-700 mb-2">
            API Version
          </label>
          <select
            value={settings.api.apiVersion}
            onChange={(e) => handleSettingChange('api', 'apiVersion', e.target.value)}
            className="admin-select"
          >
            <option value="v1">v1</option>
            <option value="v2">v2 (Beta)</option>
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-admin-700 mb-2">
          Webhook URL
        </label>
        <input
          type="url"
          value={settings.api.webhookUrl}
          onChange={(e) => handleSettingChange('api', 'webhookUrl', e.target.value)}
          className="admin-input"
          placeholder="https://your-app.com/webhook"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-admin-700 mb-2">
          API Key
        </label>
        <div className="flex gap-2">
          <input
            type="password"
            value={settings.api.apiKey}
            onChange={(e) => handleSettingChange('api', 'apiKey', e.target.value)}
            className="admin-input flex-1"
          />
          <button className="admin-button-secondary">
            <Key className="w-4 h-4 mr-2" />
            Regenerate
          </button>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 bg-admin-50 rounded-lg">
          <div>
            <h4 className="font-medium text-admin-900">Allow CORS</h4>
            <p className="text-sm text-admin-600 mt-1">Enable cross-origin resource sharing</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.api.allowCors}
              onChange={(e) => handleSettingChange('api', 'allowCors', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-admin-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-admin-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between p-4 bg-admin-50 rounded-lg">
          <div>
            <h4 className="font-medium text-admin-900">Debug Mode</h4>
            <p className="text-sm text-admin-600 mt-1">Enable detailed API logging</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.api.debugMode}
              onChange={(e) => handleSettingChange('api', 'debugMode', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-admin-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-admin-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
          </label>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6 p-4 sm:p-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-admin-900 tracking-tight">System Settings</h1>
          <p className="text-admin-600 mt-1 text-sm sm:text-base">Configure system preferences and security settings</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <button className="admin-button-secondary justify-center sm:justify-start">
            <Download className="w-4 h-4 mr-2" />
            Export Config
          </button>
          <button 
            onClick={handleSave}
            className="admin-button-primary justify-center sm:justify-start"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </button>
        </div>
      </div>

      {/* Settings Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-xl border border-admin-200/50 shadow-sm p-4">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center gap-3 px-4 py-3 text-left rounded-lg transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-primary-100 text-primary-700 border border-primary-200'
                        : 'text-admin-600 hover:bg-admin-50 hover:text-admin-900'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-xl border border-admin-200/50 shadow-sm p-6">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-admin-900">
                {tabs.find(tab => tab.id === activeTab)?.name} Settings
              </h2>
              <p className="text-admin-600 mt-1">
                {activeTab === 'general' && 'Configure basic site settings and preferences'}
                {activeTab === 'notifications' && 'Manage notification preferences and alerts'}
                {activeTab === 'security' && 'Configure security policies and authentication'}
                {activeTab === 'api' && 'Manage API settings and integrations'}
              </p>
            </div>

            {activeTab === 'general' && renderGeneralSettings()}
            {activeTab === 'notifications' && renderNotificationSettings()}
            {activeTab === 'security' && renderSecuritySettings()}
            {activeTab === 'api' && renderApiSettings()}

            {/* Save Button */}
            <div className="flex justify-end pt-6 border-t border-admin-200 mt-8">
              <button
                onClick={handleSave}
                className="admin-button-primary"
              >
                <Save className="w-4 h-4 mr-2" />
                Save {tabs.find(tab => tab.id === activeTab)?.name} Settings
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl border border-admin-200/50 shadow-sm p-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <Upload className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-admin-900">Backup Settings</h3>
              <p className="text-sm text-admin-600">Create a backup of current settings</p>
            </div>
          </div>
          <button className="admin-button-secondary w-full">
            Create Backup
          </button>
        </div>

        <div className="bg-white rounded-xl border border-admin-200/50 shadow-sm p-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <Download className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-admin-900">Restore Settings</h3>
              <p className="text-sm text-admin-600">Restore from a previous backup</p>
            </div>
          </div>
          <button className="admin-button-secondary w-full">
            Restore Backup
          </button>
        </div>

        <div className="bg-white rounded-xl border border-admin-200/50 shadow-sm p-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
              <SettingsIcon className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <h3 className="font-semibold text-admin-900">Reset Settings</h3>
              <p className="text-sm text-admin-600">Reset all settings to defaults</p>
            </div>
          </div>
          <button className="admin-button-danger w-full">
            Reset to Defaults
          </button>
        </div>
      </div>
    </div>
  );
};

export default Settings;
