import { useState, useEffect } from 'react';
import { 
  TrendingUp, Users, Eye, DollarSign, 
  Calendar, Download, Filter, BarChart3,
  <PERSON><PERSON><PERSON>, Activity, ArrowUpRight, ArrowDownRight
} from 'lucide-react';
import { 
  <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  BarChart, Bar, <PERSON><PERSON> as Re<PERSON>rtsPie<PERSON>hart, Pie, Cell, AreaChart, Area
} from 'recharts';

const Analytics = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('users');

  // Mock data
  const [analyticsData, setAnalyticsData] = useState({
    overview: {
      totalUsers: 12847,
      activeUsers: 8934,
      pageViews: 156789,
      revenue: 89432,
      conversionRate: 12.5,
      avgSessionDuration: 245
    },
    trends: {
      users: [
        { date: '2024-01-15', value: 1200, previous: 1100 },
        { date: '2024-01-16', value: 1350, previous: 1250 },
        { date: '2024-01-17', value: 1180, previous: 1080 },
        { date: '2024-01-18', value: 1420, previous: 1320 },
        { date: '2024-01-19', value: 1580, previous: 1480 },
        { date: '2024-01-20', value: 1650, previous: 1550 },
        { date: '2024-01-21', value: 1720, previous: 1620 }
      ],
      revenue: [
        { date: '2024-01-15', value: 2400, previous: 2200 },
        { date: '2024-01-16', value: 2650, previous: 2450 },
        { date: '2024-01-17', value: 2380, previous: 2180 },
        { date: '2024-01-18', value: 2820, previous: 2620 },
        { date: '2024-01-19', value: 3180, previous: 2980 },
        { date: '2024-01-20', value: 3350, previous: 3150 },
        { date: '2024-01-21', value: 3520, previous: 3320 }
      ]
    },
    topPages: [
      { page: '/side-hustles', views: 15420, bounce: 32.5 },
      { page: '/ai-tools', views: 12750, bounce: 28.3 },
      { page: '/freelancing-guide', views: 11890, bounce: 35.7 },
      { page: '/dropshipping-course', views: 9650, bounce: 41.2 },
      { page: '/personal-branding', views: 8340, bounce: 29.8 }
    ],
    userSources: [
      { name: 'Organic Search', value: 45, color: '#3b82f6' },
      { name: 'Social Media', value: 25, color: '#10b981' },
      { name: 'Direct', value: 15, color: '#f59e0b' },
      { name: 'Referral', value: 10, color: '#8b5cf6' },
      { name: 'Email', value: 5, color: '#ef4444' }
    ],
    deviceBreakdown: [
      { name: 'Desktop', value: 55, color: '#3b82f6' },
      { name: 'Mobile', value: 35, color: '#10b981' },
      { name: 'Tablet', value: 10, color: '#f59e0b' }
    ]
  });

  const getChangePercentage = (current, previous) => {
    return ((current - previous) / previous * 100).toFixed(1);
  };

  const StatCard = ({ title, value, change, changeType, color = 'blue', suffix = '' }) => {
    const bgColorClasses = {
      blue: 'from-blue-50 to-blue-100',
      green: 'from-green-50 to-green-100',
      yellow: 'from-yellow-50 to-yellow-100',
      purple: 'from-purple-50 to-purple-100',
      red: 'from-red-50 to-red-100'
    };

    const accentColorClasses = {
      blue: 'from-blue-500 to-blue-600',
      green: 'from-green-500 to-green-600',
      yellow: 'from-yellow-500 to-yellow-600',
      purple: 'from-purple-500 to-purple-600',
      red: 'from-red-500 to-red-600'
    };

    return (
      <div className={`relative overflow-hidden rounded-xl border border-admin-200/50 bg-gradient-to-br ${bgColorClasses[color]} p-6 shadow-sm hover:shadow-md transition-all duration-200`}>
        <div className="w-full">
          <p className="text-sm font-medium text-admin-600 mb-2">{title}</p>
          <p className="text-2xl font-bold text-admin-900 mb-2 break-words">
            {typeof value === 'number' ? value.toLocaleString() : value}{suffix}
          </p>
          {change && (
            <div className={`flex items-center text-xs font-medium ${
              changeType === 'increase' ? 'text-green-600' : 'text-red-600'
            }`}>
              {changeType === 'increase' ? (
                <ArrowUpRight className="w-3 h-3 mr-1 flex-shrink-0" />
              ) : (
                <ArrowDownRight className="w-3 h-3 mr-1 flex-shrink-0" />
              )}
              <span>{change}% vs last period</span>
            </div>
          )}
        </div>
        <div className={`absolute inset-0 bg-gradient-to-br ${accentColorClasses[color]} opacity-5 pointer-events-none`}></div>
      </div>
    );
  };

  return (
    <div className="space-y-6 p-4 sm:p-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-admin-900 tracking-tight">Analytics Dashboard</h1>
          <p className="text-admin-600 mt-1 text-sm sm:text-base">Track performance and user engagement metrics</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="admin-select focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <button className="admin-button-secondary justify-center sm:justify-start">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 sm:gap-6">
        <StatCard
          title="Total Users"
          value={analyticsData.overview.totalUsers}
          change={15.3}
          changeType="increase"
          color="blue"
        />
        <StatCard
          title="Active Users"
          value={analyticsData.overview.activeUsers}
          change={8.2}
          changeType="increase"
          color="green"
        />
        <StatCard
          title="Page Views"
          value={analyticsData.overview.pageViews}
          change={12.1}
          changeType="increase"
          color="purple"
        />
        <StatCard
          title="Revenue"
          value={analyticsData.overview.revenue}
          change={18.7}
          changeType="increase"
          color="green"
          suffix="$"
        />
        <StatCard
          title="Conversion Rate"
          value={analyticsData.overview.conversionRate}
          change={-2.1}
          changeType="decrease"
          color="yellow"
          suffix="%"
        />
        <StatCard
          title="Avg Session"
          value={`${Math.floor(analyticsData.overview.avgSessionDuration / 60)}:${(analyticsData.overview.avgSessionDuration % 60).toString().padStart(2, '0')}`}
          change={5.8}
          changeType="increase"
          color="purple"
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
        {/* User Growth Trend */}
        <div className="bg-white rounded-xl border border-admin-200/50 shadow-sm p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-6">
            <div>
              <h3 className="text-lg sm:text-xl font-semibold text-admin-900">User Growth Trend</h3>
              <p className="text-sm text-admin-600 mt-1">Daily user acquisition and retention</p>
            </div>
            <select
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value)}
              className="admin-select w-full sm:w-auto"
            >
              <option value="users">Users</option>
              <option value="revenue">Revenue</option>
            </select>
          </div>
          <div className="h-64 sm:h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={analyticsData.trends[selectedMetric]} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <defs>
                  <linearGradient id="colorCurrent" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                  </linearGradient>
                  <linearGradient id="colorPrevious" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#94a3b8" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#94a3b8" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                <XAxis 
                  dataKey="date" 
                  tick={{ fontSize: 12 }}
                  stroke="#64748b"
                  tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                />
                <YAxis 
                  tick={{ fontSize: 12 }}
                  stroke="#64748b"
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                  labelFormatter={(value) => new Date(value).toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                />
                <Area 
                  type="monotone" 
                  dataKey="previous" 
                  stroke="#94a3b8" 
                  fillOpacity={1} 
                  fill="url(#colorPrevious)"
                  name="Previous Period"
                />
                <Area 
                  type="monotone" 
                  dataKey="value" 
                  stroke="#3b82f6" 
                  fillOpacity={1} 
                  fill="url(#colorCurrent)"
                  name="Current Period"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Traffic Sources */}
        <div className="bg-white rounded-xl border border-admin-200/50 shadow-sm p-6">
          <div className="mb-6">
            <h3 className="text-lg sm:text-xl font-semibold text-admin-900">Traffic Sources</h3>
            <p className="text-sm text-admin-600 mt-1">Where your users are coming from</p>
          </div>
          <div className="h-64 sm:h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <Pie
                  data={analyticsData.userSources}
                  cx="50%"
                  cy="50%"
                  outerRadius="70%"
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  labelLine={false}
                  fontSize={12}
                >
                  {analyticsData.userSources.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Additional Analytics Sections */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
        {/* Top Pages */}
        <div className="xl:col-span-2 bg-white rounded-xl border border-admin-200/50 shadow-sm p-6">
          <div className="mb-6">
            <h3 className="text-lg sm:text-xl font-semibold text-admin-900">Top Performing Pages</h3>
            <p className="text-sm text-admin-600 mt-1">Most visited pages and their performance</p>
          </div>
          <div className="space-y-4">
            {analyticsData.topPages.map((page, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-admin-50/50 rounded-lg hover:bg-admin-100/50 transition-colors">
                <div className="flex items-center gap-4 flex-1 min-w-0">
                  <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-primary-600 font-semibold text-sm">{index + 1}</span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-admin-900 truncate">{page.page}</h4>
                    <div className="flex items-center gap-4 mt-1 text-sm text-admin-600">
                      <span>{page.views.toLocaleString()} views</span>
                      <span>Bounce: {page.bounce}%</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-16 bg-admin-200 rounded-full h-2">
                    <div
                      className="bg-primary-500 h-2 rounded-full"
                      style={{ width: `${(page.views / analyticsData.topPages[0].views) * 100}%` }}
                    ></div>
                  </div>
                  <button className="p-2 text-admin-400 hover:text-primary-600 transition-colors">
                    <Eye className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Device Breakdown */}
        <div className="bg-white rounded-xl border border-admin-200/50 shadow-sm p-6">
          <div className="mb-6">
            <h3 className="text-lg sm:text-xl font-semibold text-admin-900">Device Breakdown</h3>
            <p className="text-sm text-admin-600 mt-1">User device preferences</p>
          </div>
          <div className="space-y-4">
            {analyticsData.deviceBreakdown.map((device, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: device.color }}
                  ></div>
                  <span className="font-medium text-admin-900">{device.name}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-20 bg-admin-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${device.value}%`,
                        backgroundColor: device.color
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-admin-600 w-8 text-right">{device.value}%</span>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 pt-6 border-t border-admin-200">
            <h4 className="font-medium text-admin-900 mb-3">Quick Stats</h4>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-admin-600">Mobile Users</span>
                <span className="font-medium text-admin-900">35%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-admin-600">Desktop Users</span>
                <span className="font-medium text-admin-900">55%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-admin-600">Tablet Users</span>
                <span className="font-medium text-admin-900">10%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Real-time Activity */}
      <div className="bg-white rounded-xl border border-admin-200/50 shadow-sm p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
          <div>
            <h3 className="text-lg sm:text-xl font-semibold text-admin-900">Real-time Activity</h3>
            <p className="text-sm text-admin-600 mt-1">Live user activity and engagement</p>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-admin-600">Live</span>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-admin-900">247</div>
            <div className="text-sm text-admin-600 mt-1">Active Users</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-admin-900">1,234</div>
            <div className="text-sm text-admin-600 mt-1">Page Views (1h)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-admin-900">89</div>
            <div className="text-sm text-admin-600 mt-1">New Signups</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-admin-900">$2,456</div>
            <div className="text-sm text-admin-600 mt-1">Revenue (24h)</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
