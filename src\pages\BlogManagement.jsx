import { useState, useEffect } from 'react';
import { 
  Search, Filter, Plus, Edit, Trash2, Eye, 
  Calendar, User, Tag, TrendingUp, Clock,
  FileText, MoreVertical, ChevronLeft, ChevronRight
} from 'lucide-react';
import { format } from 'date-fns';

const BlogManagement = () => {
  const [posts, setPosts] = useState([]);
  const [filteredPosts, setFilteredPosts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterCategory, setFilterCategory] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [postsPerPage] = useState(10);

  // Mock data
  useEffect(() => {
    const mockPosts = [
      {
        id: 1,
        title: "10 Side Hustles That Can Make You $1000+ Monthly",
        excerpt: "Discover proven side hustles that successful entrepreneurs use to generate substantial monthly income...",
        author: "<PERSON>",
        category: "Side Hustles",
        status: "published",
        publishDate: "2024-01-15",
        views: 15420,
        likes: 342,
        comments: 89,
        featured: true,
        tags: ["side-hustle", "income", "entrepreneurship"]
      },
      {
        id: 2,
        title: "The Complete Guide to Dropshipping in 2024",
        excerpt: "Everything you need to know about starting a successful dropshipping business this year...",
        author: "Mike Chen",
        category: "E-commerce",
        status: "draft",
        publishDate: "2024-01-20",
        views: 8930,
        likes: 156,
        comments: 34,
        featured: false,
        tags: ["dropshipping", "e-commerce", "business"]
      },
      {
        id: 3,
        title: "AI Tools Every Entrepreneur Should Know About",
        excerpt: "Leverage artificial intelligence to streamline your business operations and boost productivity...",
        author: "Alex Rivera",
        category: "Technology",
        status: "published",
        publishDate: "2024-01-18",
        views: 12750,
        likes: 298,
        comments: 67,
        featured: true,
        tags: ["ai", "tools", "productivity"]
      },
      {
        id: 4,
        title: "Building a Personal Brand on Social Media",
        excerpt: "Step-by-step strategies to build an authentic personal brand that attracts opportunities...",
        author: "Emma Davis",
        category: "Marketing",
        status: "review",
        publishDate: "2024-01-22",
        views: 6840,
        likes: 187,
        comments: 45,
        featured: false,
        tags: ["branding", "social-media", "marketing"]
      },
      {
        id: 5,
        title: "Freelancing Success: From Zero to Six Figures",
        excerpt: "Real strategies and tips from freelancers who built six-figure businesses from scratch...",
        author: "David Kim",
        category: "Freelancing",
        status: "published",
        publishDate: "2024-01-12",
        views: 18650,
        likes: 445,
        comments: 123,
        featured: true,
        tags: ["freelancing", "success", "income"]
      }
    ];
    setPosts(mockPosts);
    setFilteredPosts(mockPosts);
  }, []);

  // Filter and search logic
  useEffect(() => {
    let filtered = posts;

    if (searchQuery) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (filterStatus !== 'all') {
      filtered = filtered.filter(post => post.status === filterStatus);
    }

    if (filterCategory !== 'all') {
      filtered = filtered.filter(post => post.category === filterCategory);
    }

    setFilteredPosts(filtered);
    setCurrentPage(1);
  }, [searchQuery, filterStatus, filterCategory, posts]);

  // Pagination
  const indexOfLastPost = currentPage * postsPerPage;
  const indexOfFirstPost = indexOfLastPost - postsPerPage;
  const currentPosts = filteredPosts.slice(indexOfFirstPost, indexOfLastPost);
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);

  const getStatusColor = (status) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'review': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'Side Hustles': return '💼';
      case 'E-commerce': return '🛒';
      case 'Technology': return '💻';
      case 'Marketing': return '📈';
      case 'Freelancing': return '🎯';
      default: return '📝';
    }
  };

  return (
    <div className="space-y-6 p-4 sm:p-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-admin-900 tracking-tight">Blog Management</h1>
          <p className="text-admin-600 mt-1 text-sm sm:text-base">Create, edit, and manage blog posts</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <button className="admin-button-secondary justify-center sm:justify-start">
            <Eye className="w-4 h-4 mr-2" />
            Preview Site
          </button>
          <button className="admin-button-primary justify-center sm:justify-start">
            <Plus className="w-4 h-4 mr-2" />
            New Post
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <div className="relative overflow-hidden rounded-xl border border-admin-200/50 bg-gradient-to-br from-blue-50 to-blue-100 p-6 shadow-sm hover:shadow-md transition-all duration-200">
          <div className="w-full">
            <p className="text-sm font-medium text-admin-600 mb-2">Total Posts</p>
            <p className="text-2xl font-bold text-admin-900 mb-2 break-words">{posts.length.toLocaleString()}</p>
          </div>
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-5 pointer-events-none"></div>
        </div>

        <div className="relative overflow-hidden rounded-xl border border-admin-200/50 bg-gradient-to-br from-green-50 to-green-100 p-6 shadow-sm hover:shadow-md transition-all duration-200">
          <div className="w-full">
            <p className="text-sm font-medium text-admin-600 mb-2">Published</p>
            <p className="text-2xl font-bold text-admin-900 mb-2 break-words">
              {posts.filter(p => p.status === 'published').length.toLocaleString()}
            </p>
          </div>
          <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-5 pointer-events-none"></div>
        </div>

        <div className="relative overflow-hidden rounded-xl border border-admin-200/50 bg-gradient-to-br from-yellow-50 to-yellow-100 p-6 shadow-sm hover:shadow-md transition-all duration-200">
          <div className="w-full">
            <p className="text-sm font-medium text-admin-600 mb-2">Drafts</p>
            <p className="text-2xl font-bold text-admin-900 mb-2 break-words">
              {posts.filter(p => p.status === 'draft').length.toLocaleString()}
            </p>
          </div>
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-500 to-yellow-600 opacity-5 pointer-events-none"></div>
        </div>

        <div className="relative overflow-hidden rounded-xl border border-admin-200/50 bg-gradient-to-br from-purple-50 to-purple-100 p-6 shadow-sm hover:shadow-md transition-all duration-200">
          <div className="w-full">
            <p className="text-sm font-medium text-admin-600 mb-2">Total Views</p>
            <p className="text-2xl font-bold text-admin-900 mb-2 break-words">
              {posts.reduce((sum, post) => sum + post.views, 0).toLocaleString()}
            </p>
          </div>
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-5 pointer-events-none"></div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl border border-admin-200/50 shadow-sm p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-admin-400" />
              <input
                type="text"
                placeholder="Search posts by title, author, or category..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="admin-input pl-11 pr-4 w-full focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              {/* Status Filter */}
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="admin-select min-w-[140px] focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="review">Under Review</option>
              </select>

              {/* Category Filter */}
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="admin-select min-w-[140px] focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Categories</option>
                <option value="Side Hustles">Side Hustles</option>
                <option value="E-commerce">E-commerce</option>
                <option value="Technology">Technology</option>
                <option value="Marketing">Marketing</option>
                <option value="Freelancing">Freelancing</option>
              </select>
            </div>
          </div>

          <div className="flex items-center justify-between sm:justify-end gap-4">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-admin-400" />
              <span className="text-sm font-medium text-admin-600">
                {filteredPosts.length} of {posts.length} posts
              </span>
            </div>
            <button className="admin-button-secondary text-sm">
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Posts Table */}
      <div className="bg-white rounded-xl border border-admin-200/50 shadow-sm overflow-hidden">
        {/* Desktop Table */}
        <div className="hidden lg:block overflow-x-auto">
          <table className="admin-table">
            <thead>
              <tr>
                <th>Post</th>
                <th>Author</th>
                <th>Category</th>
                <th>Status</th>
                <th>Published</th>
                <th>Views</th>
                <th>Engagement</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentPosts.map((post) => (
                <tr key={post.id}>
                  <td>
                    <div className="flex items-start space-x-3">
                      <div className="w-16 h-12 bg-admin-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-2xl">{getCategoryIcon(post.category)}</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-admin-900 truncate">{post.title}</h3>
                        <p className="text-sm text-admin-600 mt-1 line-clamp-2">{post.excerpt}</p>
                        {post.featured && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mt-2">
                            Featured
                          </span>
                        )}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-primary-600 font-medium text-sm">
                          {post.author.charAt(0)}
                        </span>
                      </div>
                      <span className="font-medium text-admin-900">{post.author}</span>
                    </div>
                  </td>
                  <td>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-admin-100 text-admin-800">
                      {post.category}
                    </span>
                  </td>
                  <td>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(post.status)}`}>
                      {post.status}
                    </span>
                  </td>
                  <td>
                    <div className="text-sm">
                      <div className="font-medium text-admin-900">{format(new Date(post.publishDate), 'MMM dd, yyyy')}</div>
                      <div className="text-admin-500">{format(new Date(post.publishDate), 'h:mm a')}</div>
                    </div>
                  </td>
                  <td>
                    <div className="text-sm font-medium text-admin-900">{post.views.toLocaleString()}</div>
                  </td>
                  <td>
                    <div className="text-sm">
                      <div className="text-admin-900">{post.likes} likes</div>
                      <div className="text-admin-500">{post.comments} comments</div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-admin-400 hover:text-primary-600 transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-admin-400 hover:text-blue-600 transition-colors">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-admin-400 hover:text-red-600 transition-colors">
                        <Trash2 className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-admin-400 hover:text-admin-600 transition-colors">
                        <MoreVertical className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Mobile Card Layout */}
        <div className="lg:hidden divide-y divide-admin-200">
          {currentPosts.map((post) => (
            <div key={post.id} className="p-4 hover:bg-admin-50/50 transition-colors">
              <div className="flex items-start gap-4">
                <div className="w-16 h-12 bg-admin-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-2xl">{getCategoryIcon(post.category)}</span>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-admin-900 truncate">{post.title}</h3>
                      <p className="text-sm text-admin-600 mt-1 line-clamp-2">{post.excerpt}</p>
                    </div>
                    <div className="flex items-center gap-2 ml-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(post.status)}`}>
                        {post.status}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                    <div>
                      <span className="text-admin-500">Author:</span>
                      <p className="font-medium text-admin-700 mt-1">{post.author}</p>
                    </div>
                    <div>
                      <span className="text-admin-500">Category:</span>
                      <p className="font-medium text-admin-700 mt-1">{post.category}</p>
                    </div>
                    <div>
                      <span className="text-admin-500">Views:</span>
                      <p className="font-medium text-admin-700 mt-1">{post.views.toLocaleString()}</p>
                    </div>
                    <div>
                      <span className="text-admin-500">Published:</span>
                      <p className="font-medium text-admin-700 mt-1">{format(new Date(post.publishDate), 'MMM dd, yyyy')}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <button className="admin-button-secondary text-sm flex-1">
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </button>
                    <button className="admin-button-secondary text-sm flex-1">
                      <Edit className="w-4 h-4 mr-1" />
                      Edit
                    </button>
                    <button className="p-2 text-admin-400 hover:text-red-600 transition-colors">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 sm:px-6 py-4 border-t border-admin-200 bg-admin-50/30">
            <div className="text-sm text-admin-600 text-center sm:text-left">
              Showing <span className="font-medium">{indexOfFirstPost + 1}</span> to <span className="font-medium">{Math.min(indexOfLastPost, filteredPosts.length)}</span> of <span className="font-medium">{filteredPosts.length}</span> posts
            </div>
            <div className="flex items-center justify-center gap-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="admin-button-secondary disabled:opacity-50 disabled:cursor-not-allowed text-sm px-3 py-2"
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                Previous
              </button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`w-8 h-8 text-sm rounded-lg transition-colors ${
                        currentPage === pageNum
                          ? 'bg-primary-600 text-white'
                          : 'text-admin-600 hover:bg-admin-100'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="admin-button-secondary disabled:opacity-50 disabled:cursor-not-allowed text-sm px-3 py-2"
              >
                Next
                <ChevronRight className="w-4 h-4 ml-1" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BlogManagement;
